# MetruyenScraper Configuration
scraper:
  # Browser settings
  browser:
    headless: true
    timeout: 30000
    viewport:
      width: 1920
      height: 1080
    
  # Anti-detection settings
  stealth:
    user_agents:
      - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    delays:
      min_delay: 2
      max_delay: 5
      page_load_delay: 3
    
    proxy:
      enabled: false
      rotation: false
      list: []
  
  # Retry settings
  retry:
    max_attempts: 3
    backoff_factor: 2
    timeout: 60

# Target website configuration
targets:
  metruyencv:
    base_url: "https://metruyencv.com"
    selectors:
      title: "h1, .title, [class*='title']"
      content: "#chapter-content"
      chapter_nav: "a[href*='chuong']"
      next_chapter: "button[data-x-ref='nextId']"
      prev_chapter: "button[data-x-ref='prevId']"
      locked_content: ".chapter-locked, [data-locked], .vip-content, .premium-content"
    
    patterns:
      chapter_url: "/truyen/.+/chuong-\\d+"
      story_url: "/truyen/.+"
    
    wait_conditions:
      - "networkidle"
      - "domcontentloaded"

# Output settings
output:
  formats:
    - json
    - csv
  
  json:
    pretty: true
    encoding: "utf-8"
  
  csv:
    encoding: "utf-8-sig"
    delimiter: ","

# Logging
logging:
  level: "INFO"
  file: "scraper.log"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
