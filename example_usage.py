"""
Example usage of MetruyenScraper
Demonstrates various scraping scenarios and configurations
"""

import asyncio
from src.metruyenscraper import MetruyenScraper


async def example_single_url():
    """Example: Scrape a single URL"""
    print("=== Example 1: Single URL Scraping ===")
    
    async with MetruyenScraper() as scraper:
        # Test the example URL you provided
        url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
        
        # First, test the site
        test_results = await scraper.test_target_site(url)
        print(f"Test results: {test_results}")
        
        # Scrape the URL
        data = await scraper.scrape_url(url)
        if data:
            print(f"Title: {data.get('title', 'N/A')}")
            print(f"Content locked: {data.get('is_locked', False)}")
            print(f"Has content: {bool(data.get('content'))}")
            print(f"Navigation links: {len(data.get('navigation', {}).get('chapters', []))}")
        
        # Export data
        exported_files = scraper.export_data()
        print(f"Data exported to: {exported_files}")


async def example_multiple_urls():
    """Example: Scrape multiple URLs concurrently"""
    print("\n=== Example 2: Multiple URLs Scraping ===")
    
    urls = [
        "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24",
        "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-23",
        "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-22"
    ]
    
    async with MetruyenScraper() as scraper:
        results = await scraper.scrape_urls(urls, max_concurrent=2)
        
        print(f"Successfully scraped {len(results)} out of {len(urls)} URLs")
        
        for i, result in enumerate(results):
            print(f"URL {i+1}: {result.get('title', 'N/A')}")
        
        # Get statistics
        stats = scraper.get_statistics()
        print(f"Statistics: {stats}")


async def example_story_chapters():
    """Example: Scrape all chapters of a story"""
    print("\n=== Example 3: Story Chapters Scraping ===")
    
    story_url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap"
    
    async with MetruyenScraper() as scraper:
        # Scrape first 5 chapters only for demo
        results = await scraper.scrape_story_chapters(story_url, max_chapters=5)
        
        print(f"Scraped {len(results)} items (story + chapters)")
        
        for i, result in enumerate(results):
            print(f"Item {i+1}: {result.get('title', 'N/A')}")
            if result.get('is_locked'):
                print("  -> Content is locked")


async def example_with_custom_config():
    """Example: Using custom configuration"""
    print("\n=== Example 4: Custom Configuration ===")
    
    # You can modify config.yaml or create a new config file
    async with MetruyenScraper("config.yaml") as scraper:
        # Validate configuration
        validation = scraper.validate_configuration()
        print(f"Configuration valid: {validation['valid']}")
        
        if validation['errors']:
            print(f"Errors: {validation['errors']}")
        if validation['warnings']:
            print(f"Warnings: {validation['warnings']}")
        
        # Test scraping with current config
        test_url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
        test_results = await scraper.test_target_site(test_url)
        print(f"Site test results: {test_results}")


async def example_error_handling():
    """Example: Error handling and recovery"""
    print("\n=== Example 5: Error Handling ===")
    
    # URLs with potential issues
    test_urls = [
        "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24",
        "https://metruyencv.com/invalid-url-404",  # This will fail
        "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-23"
    ]
    
    async with MetruyenScraper() as scraper:
        results = await scraper.scrape_urls(test_urls)
        
        print(f"Successful results: {len(results)}")
        
        # Get error statistics
        stats = scraper.get_statistics()
        error_stats = stats['error_statistics']
        
        print(f"Total errors: {error_stats['total_errors']}")
        print(f"Error breakdown: {error_stats['error_breakdown']}")
        
        failed_urls = stats['failed_urls']
        if failed_urls:
            print("Failed URLs:")
            for failed in failed_urls:
                print(f"  - {failed['url']}: {failed['error_type']}")


async def example_data_export():
    """Example: Data export in different formats"""
    print("\n=== Example 6: Data Export ===")
    
    async with MetruyenScraper() as scraper:
        # Scrape some data first
        url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
        await scraper.scrape_url(url)
        
        # Export in different formats
        exported_files = scraper.export_data("output")
        
        print("Exported files:")
        for format_type, file_path in exported_files.items():
            print(f"  {format_type.upper()}: {file_path}")
        
        # Get data statistics
        stats = scraper.get_statistics()
        data_stats = stats['data_statistics']
        print(f"Data statistics: {data_stats}")


def run_example(example_func):
    """Helper function to run async examples"""
    try:
        asyncio.run(example_func())
    except KeyboardInterrupt:
        print("\nExample interrupted by user")
    except Exception as e:
        print(f"Example failed: {e}")


if __name__ == "__main__":
    print("MetruyenScraper Examples")
    print("=" * 50)
    
    # Run examples
    examples = [
        ("Single URL", example_single_url),
        ("Multiple URLs", example_multiple_urls),
        ("Story Chapters", example_story_chapters),
        ("Custom Config", example_with_custom_config),
        ("Error Handling", example_error_handling),
        ("Data Export", example_data_export)
    ]
    
    for name, func in examples:
        print(f"\nRunning {name} example...")
        try:
            run_example(func)
        except Exception as e:
            print(f"Failed to run {name} example: {e}")
        
        input("\nPress Enter to continue to next example...")
    
    print("\nAll examples completed!")


# Additional utility functions for advanced usage
async def scrape_with_custom_selectors():
    """Example: Using custom CSS selectors"""
    from src.config_manager import ConfigManager
    
    # Load config and modify selectors
    config = ConfigManager()
    
    # Add custom selectors for a different site
    config.update_config('targets.custom_site', {
        'base_url': 'https://example.com',
        'selectors': {
            'title': 'h1.custom-title',
            'content': '.custom-content',
            'next_chapter': 'a.next-link'
        }
    })
    
    async with MetruyenScraper() as scraper:
        # Use the custom target
        data = await scraper.scrape_url('https://example.com/page', 'custom_site')
        return data


async def scrape_with_proxy():
    """Example: Using proxy configuration"""
    from src.config_manager import ConfigManager
    
    config = ConfigManager()
    
    # Enable proxy
    config.update_config('scraper.stealth.proxy.enabled', True)
    config.update_config('scraper.stealth.proxy.list', [
        {
            'server': 'http://proxy-server:port',
            'username': 'user',
            'password': 'pass'
        }
    ])
    
    async with MetruyenScraper() as scraper:
        # Scraping will now use proxy
        data = await scraper.scrape_url('https://metruyencv.com/some-page')
        return data
