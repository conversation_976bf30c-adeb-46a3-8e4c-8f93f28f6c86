#!/usr/bin/env python3
"""
Command-line interface for MetruyenScraper
Provides easy-to-use CLI for common scraping tasks
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.metruyenscraper import MetruyenScraper


async def scrape_single_url(url: str, output_dir: str = "output"):
    """Scrape a single URL"""
    print(f"Scraping URL: {url}")
    
    async with MetruyenScraper() as scraper:
        data = await scraper.scrape_url(url)
        logger.info(f"Scraped data: {data}")
        if data:
            print(f"✓ Successfully scraped: {data.get('title', 'Untitled')}")
            print(f"  Content locked: {data.get('is_locked', False)}")
            print(f"  Has content: {bool(data.get('content'))}")
            
            # Export data
            exported_files = scraper.export_data(output_dir)
            print(f"✓ Data exported to: {output_dir}")
            for format_type, file_path in exported_files.items():
                print(f"  {format_type.upper()}: {file_path}")
        else:
            print("✗ Failed to scrape URL")
            return False
    
    return True


async def scrape_multiple_urls(urls_file: str, output_dir: str = "output", max_concurrent: int = 3):
    """Scrape multiple URLs from a file"""
    urls_path = Path(urls_file)
    
    if not urls_path.exists():
        print(f"✗ URLs file not found: {urls_file}")
        return False
    
    # Read URLs from file
    with open(urls_path, 'r', encoding='utf-8') as f:
        urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    if not urls:
        print("✗ No URLs found in file")
        return False
    
    print(f"Scraping {len(urls)} URLs with max {max_concurrent} concurrent requests")
    
    async with MetruyenScraper() as scraper:
        results = await scraper.scrape_urls(urls, max_concurrent=max_concurrent)
        
        print(f"✓ Successfully scraped {len(results)}/{len(urls)} URLs")
        
        # Export data
        if results:
            exported_files = scraper.export_data(output_dir)
            print(f"✓ Data exported to: {output_dir}")
            for format_type, file_path in exported_files.items():
                print(f"  {format_type.upper()}: {file_path}")
        
        # Show statistics
        stats = scraper.get_statistics()
        print(f"\nStatistics:")
        print(f"  Total items: {stats['data_statistics']['total_items']}")
        print(f"  Locked content: {stats['data_statistics']['locked_content']}")
        print(f"  Total errors: {stats['error_statistics']['total_errors']}")
        
        if stats['failed_urls']:
            print(f"\nFailed URLs:")
            for failed in stats['failed_urls']:
                print(f"  - {failed['url']}: {failed['error_type']}")
    
    return True


async def scrape_story_chapters(story_url: str, output_dir: str = "output", max_chapters: int = None):
    """Scrape all chapters of a story"""
    print(f"Scraping story: {story_url}")
    if max_chapters:
        print(f"Limited to first {max_chapters} chapters")
    
    async with MetruyenScraper() as scraper:
        results = await scraper.scrape_story_chapters(
            story_url, 
            max_chapters=max_chapters
        )
        
        if results:
            print(f"✓ Successfully scraped {len(results)} items (story + chapters)")
            
            # Show chapter info
            for i, result in enumerate(results):
                title = result.get('title', 'Untitled')
                locked = " [LOCKED]" if result.get('is_locked') else ""
                print(f"  {i+1}. {title}{locked}")
            
            # Export data
            exported_files = scraper.export_data(output_dir)
            print(f"✓ Data exported to: {output_dir}")
            for format_type, file_path in exported_files.items():
                print(f"  {format_type.upper()}: {file_path}")
        else:
            print("✗ Failed to scrape story")
            return False
    
    return True


async def test_site(url: str):
    """Test scraping capabilities on a URL"""
    print(f"Testing site: {url}")
    
    async with MetruyenScraper() as scraper:
        # Validate configuration first
        validation = scraper.validate_configuration()
        print(f"Configuration valid: {validation['valid']}")
        
        if not validation['valid']:
            print("Configuration errors:")
            for error in validation['errors']:
                print(f"  - {error}")
            return False
        
        # Test the site
        test_results = await scraper.test_target_site(url)
        
        print(f"Test Results:")
        print(f"  Success: {test_results['success']}")
        print(f"  Data extracted: {test_results['data_extracted']}")
        print(f"  Content locked: {test_results['content_locked']}")
        print(f"  Navigation found: {test_results['navigation_found']}")
        
        if test_results['errors']:
            print(f"  Errors:")
            for error in test_results['errors']:
                print(f"    - {error}")
        
        return test_results['success']


def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description="MetruyenScraper - Web scraping tool for dynamic content",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s single https://metruyencv.com/truyen/example/chuong-1
  %(prog)s multiple urls.txt --max-concurrent 2
  %(prog)s story https://metruyencv.com/truyen/example --max-chapters 10
  %(prog)s test https://metruyencv.com/truyen/example/chuong-1
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Single URL command
    single_parser = subparsers.add_parser('single', help='Scrape a single URL')
    single_parser.add_argument('url', help='URL to scrape')
    single_parser.add_argument('--output', '-o', default='output', help='Output directory')
    
    # Multiple URLs command
    multiple_parser = subparsers.add_parser('multiple', help='Scrape multiple URLs from file')
    multiple_parser.add_argument('file', help='File containing URLs (one per line)')
    multiple_parser.add_argument('--output', '-o', default='output', help='Output directory')
    multiple_parser.add_argument('--max-concurrent', '-c', type=int, default=3, 
                               help='Maximum concurrent requests')
    
    # Story chapters command
    story_parser = subparsers.add_parser('story', help='Scrape all chapters of a story')
    story_parser.add_argument('url', help='Story URL')
    story_parser.add_argument('--output', '-o', default='output', help='Output directory')
    story_parser.add_argument('--max-chapters', '-m', type=int, 
                            help='Maximum number of chapters to scrape')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Test scraping on a URL')
    test_parser.add_argument('url', help='URL to test')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        if args.command == 'single':
            success = asyncio.run(scrape_single_url(args.url, args.output))
        elif args.command == 'multiple':
            success = asyncio.run(scrape_multiple_urls(
                args.file, args.output, args.max_concurrent
            ))
        elif args.command == 'story':
            success = asyncio.run(scrape_story_chapters(
                args.url, args.output, args.max_chapters
            ))
        elif args.command == 'test':
            success = asyncio.run(test_site(args.url))
        else:
            parser.print_help()
            return 1
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
