2025-07-07 10:07:16 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:16 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:16 | INFO | MetruyenScraper initialized
2025-07-07 10:07:16 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | MetruyenScraper initialized
2025-07-07 10:07:17 | INFO | Metruyen<PERSON>craper initialized
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | MetruyenScraper initialized
2025-07-07 10:07:17 | INFO | MetruyenScraper initialized
2025-07-07 10:07:17 | INFO | MetruyenScraper initialized
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:18 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:18 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:18 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:18 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:39 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | INFO | MetruyenScraper initialized
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:39 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | INFO | MetruyenScraper initialized
2025-07-07 10:07:39 | INFO | MetruyenScraper initialized
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | MetruyenScraper initialized
2025-07-07 10:07:40 | INFO | MetruyenScraper initialized
2025-07-07 10:07:40 | INFO | MetruyenScraper initialized
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:08:51 | INFO | Scraper engine initialized successfully
2025-07-07 10:08:51 | INFO | MetruyenScraper started
2025-07-07 10:08:51 | INFO | Configuration validation: PASSED
2025-07-07 10:08:51 | INFO | Scraper engine closed successfully
2025-07-07 10:08:51 | INFO | MetruyenScraper closed
2025-07-07 10:09:22 | INFO | Scraper engine initialized successfully
2025-07-07 10:09:22 | INFO | MetruyenScraper started
2025-07-07 10:09:22 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:09:22 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:09:37 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator(".content, .chapter-content, [class*='content'], #chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:09:37 | ERROR | Data extraction failed: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | ERROR | Failed to scrape https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | WARNING | Attempt 1/3 failed: Unknown error: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | ERROR | Non-retryable error: Unknown error: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | ERROR | Failed to scrape https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24: Unknown error: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | WARNING | Test scraping returned no data
2025-07-07 10:09:37 | INFO | Scraper engine closed successfully
2025-07-07 10:09:37 | INFO | MetruyenScraper closed
2025-07-07 10:10:38 | INFO | Scraper engine initialized successfully
2025-07-07 10:10:38 | INFO | MetruyenScraper started
2025-07-07 10:10:38 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:10:38 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:10:50 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:10:50 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:10:50 | INFO | Test scraping successful
2025-07-07 10:10:50 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:01 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:01 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:01 | INFO | Data exported to json: test_output\scraped_data_20250707_101101.json
2025-07-07 10:11:01 | INFO | Data exported to csv: test_output\scraped_data_20250707_101101.csv
2025-07-07 10:11:01 | INFO | Scraper engine closed successfully
2025-07-07 10:11:01 | INFO | MetruyenScraper closed
2025-07-07 10:11:21 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:21 | INFO | MetruyenScraper started
2025-07-07 10:11:21 | INFO | Configuration validation: PASSED
2025-07-07 10:11:21 | INFO | Scraper engine closed successfully
2025-07-07 10:11:21 | INFO | MetruyenScraper closed
2025-07-07 10:11:21 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:21 | INFO | MetruyenScraper initialized
2025-07-07 10:11:21 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:22 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:22 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:22 | INFO | MetruyenScraper started
2025-07-07 10:11:22 | INFO | MetruyenScraper started
2025-07-07 10:11:22 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:22 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:22 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:22 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:33 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:33 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:33 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:33 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:33 | INFO | Test scraping successful
2025-07-07 10:11:33 | INFO | Test scraping successful
2025-07-07 10:11:33 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:33 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:45 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:45 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:45 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:45 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:45 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:45 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:45 | INFO | MetruyenScraper started
2025-07-07 10:11:45 | INFO | MetruyenScraper started
2025-07-07 10:11:45 | INFO | MetruyenScraper started
2025-07-07 10:11:45 | INFO | Data exported to json: test_output\scraped_data_20250707_101145.json
2025-07-07 10:11:45 | INFO | Data exported to json: test_output\scraped_data_20250707_101145.json
2025-07-07 10:11:45 | INFO | Data exported to json: test_output\scraped_data_20250707_101145.json
2025-07-07 10:11:45 | INFO | Data exported to csv: test_output\scraped_data_20250707_101145.csv
2025-07-07 10:11:45 | INFO | Data exported to csv: test_output\scraped_data_20250707_101145.csv
2025-07-07 10:11:45 | INFO | Data exported to csv: test_output\scraped_data_20250707_101145.csv
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:46 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:46 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:46 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:46 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:46 | INFO | MetruyenScraper started
2025-07-07 10:11:46 | INFO | MetruyenScraper started
2025-07-07 10:11:46 | INFO | MetruyenScraper started
2025-07-07 10:11:46 | INFO | MetruyenScraper started
2025-07-07 10:11:46 | INFO | Starting to scrape 2 URLs
2025-07-07 10:11:46 | INFO | Starting to scrape 2 URLs
2025-07-07 10:11:46 | INFO | Starting to scrape 2 URLs
2025-07-07 10:11:46 | INFO | Starting to scrape 2 URLs
2025-07-07 10:11:46 | INFO | Scraping URL: https://metruyencv.com/invalid-page-404
2025-07-07 10:11:46 | INFO | Scraping URL: https://metruyencv.com/invalid-page-404
2025-07-07 10:11:46 | INFO | Scraping URL: https://metruyencv.com/invalid-page-404
2025-07-07 10:11:46 | INFO | Scraping URL: https://metruyencv.com/invalid-page-404
2025-07-07 10:11:46 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:46 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:46 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:46 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Failed to load page: HTTP 404
2025-07-07 10:11:46 | WARNING | Attempt 1/3 failed: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | WARNING | Attempt 1/3 failed: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | WARNING | Attempt 1/3 failed: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | WARNING | Attempt 1/3 failed: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Non-retryable error: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Non-retryable error: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Non-retryable error: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Non-retryable error: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:49 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | WARNING | Attempt 1/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | WARNING | Attempt 1/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | WARNING | Attempt 1/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | WARNING | Attempt 1/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | INFO | Retrying in 0.989 seconds...
2025-07-07 10:11:49 | INFO | Retrying in 0.989 seconds...
2025-07-07 10:11:49 | INFO | Retrying in 0.989 seconds...
2025-07-07 10:11:49 | INFO | Retrying in 0.989 seconds...
2025-07-07 10:11:50 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:50 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:50 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:50 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:52 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | WARNING | Attempt 2/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | WARNING | Attempt 2/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | WARNING | Attempt 2/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | WARNING | Attempt 2/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | INFO | Retrying in 2.086 seconds...
2025-07-07 10:11:52 | INFO | Retrying in 2.086 seconds...
2025-07-07 10:11:52 | INFO | Retrying in 2.086 seconds...
2025-07-07 10:11:52 | INFO | Retrying in 2.086 seconds...
2025-07-07 10:11:54 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:54 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:54 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:54 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Attempt 3/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Attempt 3/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Attempt 3/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Attempt 3/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | All 3 attempts failed
2025-07-07 10:11:55 | ERROR | All 3 attempts failed
2025-07-07 10:11:55 | ERROR | All 3 attempts failed
2025-07-07 10:11:55 | ERROR | All 3 attempts failed
2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | INFO | Successfully scraped 0/2 URLs
2025-07-07 10:11:55 | INFO | Successfully scraped 0/2 URLs
2025-07-07 10:11:55 | INFO | Successfully scraped 0/2 URLs
2025-07-07 10:11:55 | INFO | Successfully scraped 0/2 URLs
2025-07-07 10:11:55 | INFO | Scraper engine closed successfully
2025-07-07 10:11:55 | INFO | Scraper engine closed successfully
2025-07-07 10:11:55 | INFO | Scraper engine closed successfully
2025-07-07 10:11:55 | INFO | Scraper engine closed successfully
2025-07-07 10:11:55 | INFO | MetruyenScraper closed
2025-07-07 10:11:55 | INFO | MetruyenScraper closed
2025-07-07 10:11:55 | INFO | MetruyenScraper closed
2025-07-07 10:11:55 | INFO | MetruyenScraper closed
2025-07-07 10:12:05 | INFO | Scraper engine initialized successfully
2025-07-07 10:12:05 | INFO | MetruyenScraper started
2025-07-07 10:12:05 | INFO | Configuration validation: PASSED
2025-07-07 10:12:05 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:12:05 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:12:17 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:12:17 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:12:17 | INFO | Test scraping successful
2025-07-07 10:12:17 | INFO | Scraper engine closed successfully
2025-07-07 10:12:17 | INFO | MetruyenScraper closed
2025-07-07 10:22:11 | INFO | Scraper engine initialized successfully
2025-07-07 10:22:11 | INFO | MetruyenScraper started
2025-07-07 10:22:11 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:22:15 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': "#chapter-content, .content, .chapter-content, [class*='content']", 'chapter_nav': "a[href*='chuong']", 'next_chapter': "a[href*='chuong']:has-text('sau'), .next-chapter", 'prev_chapter': "a[href*='chuong']:has-text('trước'), .prev-chapter", 'locked_content': "[class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content"}
2025-07-07 10:22:15 | INFO | ⏳ Waiting for content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:25 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:22:25 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:22:25 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 24
2025-07-07 10:22:25 | INFO | 📊 Document ready state: complete
2025-07-07 10:22:25 | INFO | 🔍 Searching for content with selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:25 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:22:25 | INFO |     Found 1 elements
2025-07-07 10:22:25 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:22:25 | INFO |         Text preview: ''
2025-07-07 10:22:25 | INFO |   Testing selector 2: .content
2025-07-07 10:22:25 | INFO |     Found 0 elements
2025-07-07 10:22:25 | INFO |   Testing selector 3: .chapter-content
2025-07-07 10:22:25 | INFO |     Found 0 elements
2025-07-07 10:22:25 | INFO |   Testing selector 4: [class*='content']
2025-07-07 10:22:25 | INFO |     Found 0 elements
2025-07-07 10:22:25 | INFO | 🔒 Checking for locked content with selector: [class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content
2025-07-07 10:22:25 | INFO |     Found 18 locked content indicators
2025-07-07 10:22:25 | INFO |       Locked indicator 1: 'Màu nền [ngày]'
2025-07-07 10:22:25 | INFO |       Locked indicator 2: 'Màu chữ [ngày]'
2025-07-07 10:22:25 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858545.png
2025-07-07 10:22:25 | INFO | 🔒 Checking for locked content...
2025-07-07 10:22:25 | INFO | 🔒 Locked content detected: True
2025-07-07 10:22:25 | INFO | 🔒 Locked content text: 'Màu nền [ngày]'
2025-07-07 10:22:25 | INFO | 📝 Extracting main content...
2025-07-07 10:22:25 | INFO | 📝 Using content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:25 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:22:25 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:22:25 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:22:25 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:22:25 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:22:25 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:22:25 | INFO | 📝 Trying selector 2: .content
2025-07-07 10:22:25 | INFO | 📝 Found 0 elements with selector: .content
2025-07-07 10:22:25 | INFO | 📝 Trying selector 3: .chapter-content
2025-07-07 10:22:25 | INFO | 📝 Found 0 elements with selector: .chapter-content
2025-07-07 10:22:25 | INFO | 📝 Trying selector 4: [class*='content']
2025-07-07 10:22:25 | INFO | 📝 Found 0 elements with selector: [class*='content']
2025-07-07 10:22:25 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:22:25 | INFO | 🔄 Found content with main: 1023 chars
2025-07-07 10:22:25 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:22:25 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:22:25 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:22:25 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:22:25 | WARNING | 🔄 JavaScript method 1 failed: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:291:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-07 10:22:25 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:22:25 | WARNING | 🔄 JavaScript method 2 failed: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:291:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-07 10:22:25 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:22:25 | INFO | Scraper engine closed successfully
2025-07-07 10:22:25 | INFO | MetruyenScraper closed
2025-07-07 10:22:30 | INFO | Configuration loaded from config.yaml
2025-07-07 10:22:30 | INFO | MetruyenScraper initialized
2025-07-07 10:22:30 | INFO | Configuration loaded from config.yaml
2025-07-07 10:22:31 | INFO | Scraper engine initialized successfully
2025-07-07 10:22:31 | INFO | Scraper engine initialized successfully
2025-07-07 10:22:31 | INFO | MetruyenScraper started
2025-07-07 10:22:31 | INFO | MetruyenScraper started
2025-07-07 10:22:31 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:22:31 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:22:32 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': "#chapter-content, .content, .chapter-content, [class*='content']", 'chapter_nav': "a[href*='chuong']", 'next_chapter': "a[href*='chuong']:has-text('sau'), .next-chapter", 'prev_chapter': "a[href*='chuong']:has-text('trước'), .prev-chapter", 'locked_content': "[class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content"}
2025-07-07 10:22:32 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': "#chapter-content, .content, .chapter-content, [class*='content']", 'chapter_nav': "a[href*='chuong']", 'next_chapter': "a[href*='chuong']:has-text('sau'), .next-chapter", 'prev_chapter': "a[href*='chuong']:has-text('trước'), .prev-chapter", 'locked_content': "[class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content"}
2025-07-07 10:22:32 | INFO | ⏳ Waiting for content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:32 | INFO | ⏳ Waiting for content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:42 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:22:42 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:22:42 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:22:42 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:22:42 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 1
2025-07-07 10:22:42 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 1
2025-07-07 10:22:42 | INFO | 📊 Document ready state: complete
2025-07-07 10:22:42 | INFO | 📊 Document ready state: complete
2025-07-07 10:22:42 | INFO | 🔍 Searching for content with selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:42 | INFO | 🔍 Searching for content with selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:42 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:22:42 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:22:42 | INFO |     Found 1 elements
2025-07-07 10:22:42 | INFO |     Found 1 elements
2025-07-07 10:22:42 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:22:42 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:22:42 | INFO |         Text preview: ''
2025-07-07 10:22:42 | INFO |         Text preview: ''
2025-07-07 10:22:42 | INFO |   Testing selector 2: .content
2025-07-07 10:22:42 | INFO |   Testing selector 2: .content
2025-07-07 10:22:42 | INFO |     Found 0 elements
2025-07-07 10:22:42 | INFO |     Found 0 elements
2025-07-07 10:22:42 | INFO |   Testing selector 3: .chapter-content
2025-07-07 10:22:42 | INFO |   Testing selector 3: .chapter-content
2025-07-07 10:22:42 | INFO |     Found 0 elements
2025-07-07 10:22:42 | INFO |     Found 0 elements
2025-07-07 10:22:42 | INFO |   Testing selector 4: [class*='content']
2025-07-07 10:22:42 | INFO |   Testing selector 4: [class*='content']
2025-07-07 10:22:42 | INFO |     Found 0 elements
2025-07-07 10:22:42 | INFO |     Found 0 elements
2025-07-07 10:22:42 | INFO | 🔒 Checking for locked content with selector: [class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content
2025-07-07 10:22:42 | INFO | 🔒 Checking for locked content with selector: [class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content
2025-07-07 10:22:42 | INFO |     Found 18 locked content indicators
2025-07-07 10:22:42 | INFO |     Found 18 locked content indicators
2025-07-07 10:22:42 | INFO |       Locked indicator 1: 'Màu nền [ngày]'
2025-07-07 10:22:42 | INFO |       Locked indicator 1: 'Màu nền [ngày]'
2025-07-07 10:22:42 | INFO |       Locked indicator 2: 'Màu chữ [ngày]'
2025-07-07 10:22:42 | INFO |       Locked indicator 2: 'Màu chữ [ngày]'
2025-07-07 10:22:42 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858562.png
2025-07-07 10:22:42 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858562.png
2025-07-07 10:22:42 | INFO | 🔒 Checking for locked content...
2025-07-07 10:22:42 | INFO | 🔒 Checking for locked content...
2025-07-07 10:22:42 | INFO | 🔒 Locked content detected: True
2025-07-07 10:22:42 | INFO | 🔒 Locked content detected: True
2025-07-07 10:22:42 | INFO | 🔒 Locked content text: 'Màu nền [ngày]'
2025-07-07 10:22:42 | INFO | 🔒 Locked content text: 'Màu nền [ngày]'
2025-07-07 10:22:42 | INFO | 📝 Extracting main content...
2025-07-07 10:22:42 | INFO | 📝 Extracting main content...
2025-07-07 10:22:42 | INFO | 📝 Using content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:42 | INFO | 📝 Using content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:42 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:22:42 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:22:42 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:22:42 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:22:42 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:22:42 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:22:42 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:22:42 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:22:42 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:22:42 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:22:42 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:22:42 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:22:42 | INFO | 📝 Trying selector 2: .content
2025-07-07 10:22:42 | INFO | 📝 Trying selector 2: .content
2025-07-07 10:22:42 | INFO | 📝 Found 0 elements with selector: .content
2025-07-07 10:22:42 | INFO | 📝 Found 0 elements with selector: .content
2025-07-07 10:22:42 | INFO | 📝 Trying selector 3: .chapter-content
2025-07-07 10:22:42 | INFO | 📝 Trying selector 3: .chapter-content
2025-07-07 10:22:42 | INFO | 📝 Found 0 elements with selector: .chapter-content
2025-07-07 10:22:42 | INFO | 📝 Found 0 elements with selector: .chapter-content
2025-07-07 10:22:42 | INFO | 📝 Trying selector 4: [class*='content']
2025-07-07 10:22:42 | INFO | 📝 Trying selector 4: [class*='content']
2025-07-07 10:22:42 | INFO | 📝 Found 0 elements with selector: [class*='content']
2025-07-07 10:22:42 | INFO | 📝 Found 0 elements with selector: [class*='content']
2025-07-07 10:22:42 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:22:42 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:22:42 | INFO | 🔄 Found content with main: 1019 chars
2025-07-07 10:22:42 | INFO | 🔄 Found content with main: 1019 chars
2025-07-07 10:22:42 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 01: Trùng sinh bắt đầu, bắt cóc vị hôn thê Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'chapte"
2025-07-07 10:22:42 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 01: Trùng sinh bắt đầu, bắt cóc vị hôn thê Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'chapte"
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:22:42 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:22:42 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:22:42 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:22:42 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:22:42 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:22:42 | WARNING | 🔄 JavaScript method 1 failed: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:291:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-07 10:22:42 | WARNING | 🔄 JavaScript method 1 failed: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:291:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-07 10:22:42 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:22:42 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:22:42 | WARNING | 🔄 JavaScript method 2 failed: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:291:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-07 10:22:42 | WARNING | 🔄 JavaScript method 2 failed: Page.evaluate: SyntaxError: Illegal return statement
    at eval (<anonymous>)
    at UtilityScript.evaluate (<anonymous>:291:30)
    at UtilityScript.<anonymous> (<anonymous>:1:44)
2025-07-07 10:22:42 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:22:42 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:22:43 | INFO | Scraper engine closed successfully
2025-07-07 10:22:43 | INFO | Scraper engine closed successfully
2025-07-07 10:22:43 | INFO | MetruyenScraper closed
2025-07-07 10:22:43 | INFO | MetruyenScraper closed
2025-07-07 10:22:48 | INFO | Configuration loaded from config.yaml
2025-07-07 10:22:48 | INFO | Configuration loaded from config.yaml
2025-07-07 10:22:48 | INFO | MetruyenScraper initialized
2025-07-07 10:22:48 | INFO | MetruyenScraper initialized
2025-07-07 10:22:48 | INFO | Configuration loaded from config.yaml
2025-07-07 10:22:48 | INFO | Configuration loaded from config.yaml
2025-07-07 10:22:48 | INFO | Scraper engine initialized successfully
2025-07-07 10:22:48 | INFO | Scraper engine initialized successfully
2025-07-07 10:22:48 | INFO | Scraper engine initialized successfully
2025-07-07 10:22:48 | INFO | MetruyenScraper started
2025-07-07 10:22:48 | INFO | MetruyenScraper started
2025-07-07 10:22:48 | INFO | MetruyenScraper started
2025-07-07 10:22:48 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:22:48 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:22:48 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:22:49 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': "#chapter-content, .content, .chapter-content, [class*='content']", 'chapter_nav': "a[href*='chuong']", 'next_chapter': "a[href*='chuong']:has-text('sau'), .next-chapter", 'prev_chapter': "a[href*='chuong']:has-text('trước'), .prev-chapter", 'locked_content': "[class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content"}
2025-07-07 10:22:49 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': "#chapter-content, .content, .chapter-content, [class*='content']", 'chapter_nav': "a[href*='chuong']", 'next_chapter': "a[href*='chuong']:has-text('sau'), .next-chapter", 'prev_chapter': "a[href*='chuong']:has-text('trước'), .prev-chapter", 'locked_content': "[class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content"}
2025-07-07 10:22:49 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': "#chapter-content, .content, .chapter-content, [class*='content']", 'chapter_nav': "a[href*='chuong']", 'next_chapter': "a[href*='chuong']:has-text('sau'), .next-chapter", 'prev_chapter': "a[href*='chuong']:has-text('trước'), .prev-chapter", 'locked_content': "[class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content"}
2025-07-07 10:22:49 | INFO | ⏳ Waiting for content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:49 | INFO | ⏳ Waiting for content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:49 | INFO | ⏳ Waiting for content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:59 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible

2025-07-07 10:22:59 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible

2025-07-07 10:22:59 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible

2025-07-07 10:22:59 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:22:59 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:22:59 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:22:59 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Convert
2025-07-07 10:22:59 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Convert
2025-07-07 10:22:59 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Convert
2025-07-07 10:22:59 | INFO | 📊 Document ready state: complete
2025-07-07 10:22:59 | INFO | 📊 Document ready state: complete
2025-07-07 10:22:59 | INFO | 📊 Document ready state: complete
2025-07-07 10:22:59 | INFO | 🔍 Searching for content with selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:59 | INFO | 🔍 Searching for content with selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:59 | INFO | 🔍 Searching for content with selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:59 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:22:59 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:22:59 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |   Testing selector 2: .content
2025-07-07 10:22:59 | INFO |   Testing selector 2: .content
2025-07-07 10:22:59 | INFO |   Testing selector 2: .content
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |   Testing selector 3: .chapter-content
2025-07-07 10:22:59 | INFO |   Testing selector 3: .chapter-content
2025-07-07 10:22:59 | INFO |   Testing selector 3: .chapter-content
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |   Testing selector 4: [class*='content']
2025-07-07 10:22:59 | INFO |   Testing selector 4: [class*='content']
2025-07-07 10:22:59 | INFO |   Testing selector 4: [class*='content']
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO |     Found 0 elements
2025-07-07 10:22:59 | INFO | 🔒 Checking for locked content with selector: [class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content
2025-07-07 10:22:59 | INFO | 🔒 Checking for locked content with selector: [class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content
2025-07-07 10:22:59 | INFO | 🔒 Checking for locked content with selector: [class*='lock'], .locked, .premium, .chapter-locked, [data-locked], .vip-content
2025-07-07 10:22:59 | INFO |     Found 2 locked content indicators
2025-07-07 10:22:59 | INFO |     Found 2 locked content indicators
2025-07-07 10:22:59 | INFO |     Found 2 locked content indicators
2025-07-07 10:22:59 | INFO |       Locked indicator 1: 'Báo cáoPhản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện SậpMang Quả PasyĐọc Tru'
2025-07-07 10:22:59 | INFO |       Locked indicator 1: 'Báo cáoPhản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện SậpMang Quả PasyĐọc Tru'
2025-07-07 10:22:59 | INFO |       Locked indicator 1: 'Báo cáoPhản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện SậpMang Quả PasyĐọc Tru'
2025-07-07 10:22:59 | INFO |       Locked indicator 2: 'Đọc TruyệnĐọc Tiếp'
2025-07-07 10:22:59 | INFO |       Locked indicator 2: 'Đọc TruyệnĐọc Tiếp'
2025-07-07 10:22:59 | INFO |       Locked indicator 2: 'Đọc TruyệnĐọc Tiếp'
2025-07-07 10:22:59 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858579.png
2025-07-07 10:22:59 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858579.png
2025-07-07 10:22:59 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858579.png
2025-07-07 10:22:59 | INFO | 🔒 Checking for locked content...
2025-07-07 10:22:59 | INFO | 🔒 Checking for locked content...
2025-07-07 10:22:59 | INFO | 🔒 Checking for locked content...
2025-07-07 10:22:59 | INFO | 🔒 Locked content detected: True
2025-07-07 10:22:59 | INFO | 🔒 Locked content detected: True
2025-07-07 10:22:59 | INFO | 🔒 Locked content detected: True
2025-07-07 10:22:59 | INFO | 🔒 Locked content text: 'Báo cáoPhản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện SậpMang Quả PasyĐọc TruyệnĐọc TiếpĐánh DấuMục Lục394Đánh GiáThảo Luận1735Chs/tuần32955Lượt đọc21Đề cử346Cất giữCòn tiếpHuyề'
2025-07-07 10:22:59 | INFO | 🔒 Locked content text: 'Báo cáoPhản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện SậpMang Quả PasyĐọc TruyệnĐọc TiếpĐánh DấuMục Lục394Đánh GiáThảo Luận1735Chs/tuần32955Lượt đọc21Đề cử346Cất giữCòn tiếpHuyề'
2025-07-07 10:22:59 | INFO | 🔒 Locked content text: 'Báo cáoPhản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện SậpMang Quả PasyĐọc TruyệnĐọc TiếpĐánh DấuMục Lục394Đánh GiáThảo Luận1735Chs/tuần32955Lượt đọc21Đề cử346Cất giữCòn tiếpHuyề'
2025-07-07 10:22:59 | INFO | 📝 Extracting main content...
2025-07-07 10:22:59 | INFO | 📝 Extracting main content...
2025-07-07 10:22:59 | INFO | 📝 Extracting main content...
2025-07-07 10:22:59 | INFO | 📝 Using content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:59 | INFO | 📝 Using content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:59 | INFO | 📝 Using content selector: #chapter-content, .content, .chapter-content, [class*='content']
2025-07-07 10:22:59 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: #chapter-content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: #chapter-content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: #chapter-content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 2: .content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 2: .content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 2: .content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: .content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: .content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: .content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 3: .chapter-content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 3: .chapter-content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 3: .chapter-content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: .chapter-content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: .chapter-content
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: .chapter-content
2025-07-07 10:22:59 | INFO | 📝 Trying selector 4: [class*='content']
2025-07-07 10:22:59 | INFO | 📝 Trying selector 4: [class*='content']
2025-07-07 10:22:59 | INFO | 📝 Trying selector 4: [class*='content']
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: [class*='content']
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: [class*='content']
2025-07-07 10:22:59 | INFO | 📝 Found 0 elements with selector: [class*='content']
2025-07-07 10:22:59 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:22:59 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:22:59 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:22:59 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:22:59 | INFO | 🔄 Found content with .break-words: 888 chars
2025-07-07 10:22:59 | INFO | 🔄 Found content with .break-words: 888 chars
2025-07-07 10:22:59 | INFO | 🔄 Found content with .break-words: 888 chars
2025-07-07 10:22:59 | INFO | 🔄 Content preview: ' Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?Xuyên qua thành hẳn phải c·hết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thán'
2025-07-07 10:22:59 | INFO | 🔄 Content preview: ' Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?Xuyên qua thành hẳn phải c·hết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thán'
2025-07-07 10:22:59 | INFO | 🔄 Content preview: ' Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?Xuyên qua thành hẳn phải c·hết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thán'
2025-07-07 10:22:59 | INFO | ✅ Alternative content extraction successful with: .break-words
2025-07-07 10:22:59 | INFO | ✅ Alternative content extraction successful with: .break-words
2025-07-07 10:22:59 | INFO | ✅ Alternative content extraction successful with: .break-words
2025-07-07 10:22:59 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:22:59 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:22:59 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:23:00 | INFO | Scraper engine closed successfully
2025-07-07 10:23:00 | INFO | Scraper engine closed successfully
2025-07-07 10:23:00 | INFO | Scraper engine closed successfully
2025-07-07 10:23:00 | INFO | MetruyenScraper closed
2025-07-07 10:23:00 | INFO | MetruyenScraper closed
2025-07-07 10:23:00 | INFO | MetruyenScraper closed
2025-07-07 10:27:50 | INFO | Scraper engine initialized successfully
2025-07-07 10:27:50 | INFO | MetruyenScraper started
2025-07-07 10:27:50 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:27:52 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': '#chapter-content', 'chapter_nav': "a[href*='chuong']", 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content'}
2025-07-07 10:27:52 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:28:02 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:28:02 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:28:02 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 24
2025-07-07 10:28:02 | INFO | 📊 Document ready state: complete
2025-07-07 10:28:02 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:28:02 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:28:02 | INFO |     Found 1 elements
2025-07-07 10:28:02 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:28:02 | INFO |         Text preview: ''
2025-07-07 10:28:02 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:28:02 | INFO |     Found 0 locked content indicators
2025-07-07 10:28:02 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858882.png
2025-07-07 10:28:02 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:28:06 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:28:06 | INFO | 🔒 Checking for locked content...
2025-07-07 10:28:06 | INFO | 🔒 Locked content detected: False
2025-07-07 10:28:06 | INFO | 📝 Extracting main content...
2025-07-07 10:28:06 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:28:06 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:28:06 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:28:06 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:28:06 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:28:06 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:28:06 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:28:06 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:28:06 | INFO | 🔄 Found content with main: 1023 chars
2025-07-07 10:28:06 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:28:06 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:28:06 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:28:06 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:28:06 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:28:06 | INFO | 🔄 JS extraction found content: 7398 chars with body
2025-07-07 10:28:06 | INFO | 🔄 JS content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:28:06 | INFO | ✅ JavaScript content extraction successful
2025-07-07 10:28:06 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:28:07 | INFO | Scraper engine closed successfully
2025-07-07 10:28:07 | INFO | MetruyenScraper closed
2025-07-07 10:28:12 | INFO | Configuration loaded from config.yaml
2025-07-07 10:28:12 | INFO | MetruyenScraper initialized
2025-07-07 10:28:12 | INFO | Configuration loaded from config.yaml
2025-07-07 10:28:12 | INFO | Scraper engine initialized successfully
2025-07-07 10:28:12 | INFO | Scraper engine initialized successfully
2025-07-07 10:28:12 | INFO | MetruyenScraper started
2025-07-07 10:28:12 | INFO | MetruyenScraper started
2025-07-07 10:28:12 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:28:12 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:28:14 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': '#chapter-content', 'chapter_nav': "a[href*='chuong']", 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content'}
2025-07-07 10:28:14 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': '#chapter-content', 'chapter_nav': "a[href*='chuong']", 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content'}
2025-07-07 10:28:14 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:28:14 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:28:24 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:28:24 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:28:24 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:28:24 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:28:24 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 1
2025-07-07 10:28:24 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 1
2025-07-07 10:28:24 | INFO | 📊 Document ready state: complete
2025-07-07 10:28:24 | INFO | 📊 Document ready state: complete
2025-07-07 10:28:24 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:28:24 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:28:24 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:28:24 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:28:24 | INFO |     Found 1 elements
2025-07-07 10:28:24 | INFO |     Found 1 elements
2025-07-07 10:28:24 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:28:24 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:28:24 | INFO |         Text preview: ''
2025-07-07 10:28:24 | INFO |         Text preview: ''
2025-07-07 10:28:24 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:28:24 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:28:24 | INFO |     Found 0 locked content indicators
2025-07-07 10:28:24 | INFO |     Found 0 locked content indicators
2025-07-07 10:28:24 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858904.png
2025-07-07 10:28:24 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858904.png
2025-07-07 10:28:24 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:28:24 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:28:28 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:28:28 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:28:28 | INFO | 🔒 Checking for locked content...
2025-07-07 10:28:28 | INFO | 🔒 Checking for locked content...
2025-07-07 10:28:28 | INFO | 🔒 Locked content detected: False
2025-07-07 10:28:28 | INFO | 🔒 Locked content detected: False
2025-07-07 10:28:28 | INFO | 📝 Extracting main content...
2025-07-07 10:28:28 | INFO | 📝 Extracting main content...
2025-07-07 10:28:28 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:28:28 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:28:28 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:28:28 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:28:28 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:28:28 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:28:28 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:28:28 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:28:28 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:28:28 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:28:28 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:28:28 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:28:28 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:28:28 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:28:28 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:28:28 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:28:28 | INFO | 🔄 Found content with main: 1019 chars
2025-07-07 10:28:28 | INFO | 🔄 Found content with main: 1019 chars
2025-07-07 10:28:28 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 01: Trùng sinh bắt đầu, bắt cóc vị hôn thê Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'chapte"
2025-07-07 10:28:28 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 01: Trùng sinh bắt đầu, bắt cóc vị hôn thê Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'chapte"
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:28:28 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:28:28 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:28:28 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:28:28 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:28:28 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:28:28 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:28:28 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:28:28 | INFO | 🔄 JS extraction found content: 7299 chars with body
2025-07-07 10:28:28 | INFO | 🔄 JS extraction found content: 7299 chars with body
2025-07-07 10:28:28 | INFO | 🔄 JS content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 01: Trùng sinh bắt đầu, bắt cóc vị hôn thê Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'chapte"
2025-07-07 10:28:28 | INFO | 🔄 JS content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 01: Trùng sinh bắt đầu, bắt cóc vị hôn thê Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'chapte"
2025-07-07 10:28:28 | INFO | ✅ JavaScript content extraction successful
2025-07-07 10:28:28 | INFO | ✅ JavaScript content extraction successful
2025-07-07 10:28:28 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:28:28 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:28:28 | INFO | Scraper engine closed successfully
2025-07-07 10:28:28 | INFO | Scraper engine closed successfully
2025-07-07 10:28:28 | INFO | MetruyenScraper closed
2025-07-07 10:28:28 | INFO | MetruyenScraper closed
2025-07-07 10:28:33 | INFO | Configuration loaded from config.yaml
2025-07-07 10:28:33 | INFO | Configuration loaded from config.yaml
2025-07-07 10:28:33 | INFO | MetruyenScraper initialized
2025-07-07 10:28:33 | INFO | MetruyenScraper initialized
2025-07-07 10:28:33 | INFO | Configuration loaded from config.yaml
2025-07-07 10:28:33 | INFO | Configuration loaded from config.yaml
2025-07-07 10:28:34 | INFO | Scraper engine initialized successfully
2025-07-07 10:28:34 | INFO | Scraper engine initialized successfully
2025-07-07 10:28:34 | INFO | Scraper engine initialized successfully
2025-07-07 10:28:34 | INFO | MetruyenScraper started
2025-07-07 10:28:34 | INFO | MetruyenScraper started
2025-07-07 10:28:34 | INFO | MetruyenScraper started
2025-07-07 10:28:34 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:28:34 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:28:34 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:28:36 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': '#chapter-content', 'chapter_nav': "a[href*='chuong']", 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content'}
2025-07-07 10:28:36 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': '#chapter-content', 'chapter_nav': "a[href*='chuong']", 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content'}
2025-07-07 10:28:36 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': '#chapter-content', 'chapter_nav': "a[href*='chuong']", 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content'}
2025-07-07 10:28:36 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:28:36 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:28:36 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:28:46 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible

2025-07-07 10:28:46 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible

2025-07-07 10:28:46 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible

2025-07-07 10:28:46 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:28:46 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:28:46 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:28:46 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Convert
2025-07-07 10:28:46 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Convert
2025-07-07 10:28:46 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Convert
2025-07-07 10:28:46 | INFO | 📊 Document ready state: complete
2025-07-07 10:28:46 | INFO | 📊 Document ready state: complete
2025-07-07 10:28:46 | INFO | 📊 Document ready state: complete
2025-07-07 10:28:46 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:28:46 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:28:46 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:28:46 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:28:46 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:28:46 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:28:46 | INFO |     Found 0 elements
2025-07-07 10:28:46 | INFO |     Found 0 elements
2025-07-07 10:28:46 | INFO |     Found 0 elements
2025-07-07 10:28:46 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:28:46 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:28:46 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:28:46 | INFO |     Found 0 locked content indicators
2025-07-07 10:28:46 | INFO |     Found 0 locked content indicators
2025-07-07 10:28:46 | INFO |     Found 0 locked content indicators
2025-07-07 10:28:46 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858926.png
2025-07-07 10:28:46 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858926.png
2025-07-07 10:28:46 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751858926.png
2025-07-07 10:28:46 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:28:46 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:28:46 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:28:50 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:28:50 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:28:50 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:28:50 | INFO | 🔒 Checking for locked content...
2025-07-07 10:28:50 | INFO | 🔒 Checking for locked content...
2025-07-07 10:28:50 | INFO | 🔒 Checking for locked content...
2025-07-07 10:28:50 | INFO | 🔒 Locked content detected: False
2025-07-07 10:28:50 | INFO | 🔒 Locked content detected: False
2025-07-07 10:28:50 | INFO | 🔒 Locked content detected: False
2025-07-07 10:28:50 | INFO | 📝 Extracting main content...
2025-07-07 10:28:50 | INFO | 📝 Extracting main content...
2025-07-07 10:28:50 | INFO | 📝 Extracting main content...
2025-07-07 10:28:50 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:28:50 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:28:50 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:28:50 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:28:50 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:28:50 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:28:50 | INFO | 📝 Found 0 elements with selector: #chapter-content
2025-07-07 10:28:50 | INFO | 📝 Found 0 elements with selector: #chapter-content
2025-07-07 10:28:50 | INFO | 📝 Found 0 elements with selector: #chapter-content
2025-07-07 10:28:50 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:28:50 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:28:50 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:28:50 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:28:50 | INFO | 🔄 Found content with .break-words: 888 chars
2025-07-07 10:28:50 | INFO | 🔄 Found content with .break-words: 888 chars
2025-07-07 10:28:50 | INFO | 🔄 Found content with .break-words: 888 chars
2025-07-07 10:28:50 | INFO | 🔄 Content preview: ' Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?Xuyên qua thành hẳn phải c·hết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thán'
2025-07-07 10:28:50 | INFO | 🔄 Content preview: ' Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?Xuyên qua thành hẳn phải c·hết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thán'
2025-07-07 10:28:50 | INFO | 🔄 Content preview: ' Làm toàn viên có thể nghe lén phản phái tiếng lòng, cái này nội dung cốt truyện còn thế nào diễn?Xuyên qua thành hẳn phải c·hết vô não phản phái, Lâm Bạch nhìn xem cột vào trên giường thanh lãnh thán'
2025-07-07 10:28:50 | INFO | ✅ Alternative content extraction successful with: .break-words
2025-07-07 10:28:50 | INFO | ✅ Alternative content extraction successful with: .break-words
2025-07-07 10:28:50 | INFO | ✅ Alternative content extraction successful with: .break-words
2025-07-07 10:28:50 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:28:50 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:28:50 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap
2025-07-07 10:28:50 | INFO | Scraper engine closed successfully
2025-07-07 10:28:50 | INFO | Scraper engine closed successfully
2025-07-07 10:28:50 | INFO | Scraper engine closed successfully
2025-07-07 10:28:50 | INFO | MetruyenScraper closed
2025-07-07 10:28:50 | INFO | MetruyenScraper closed
2025-07-07 10:28:50 | INFO | MetruyenScraper closed
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | MetruyenScraper initialized
2025-07-07 10:30:19 | INFO | MetruyenScraper initialized
2025-07-07 10:30:19 | INFO | MetruyenScraper initialized
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:19 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:19 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:19 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:19 | INFO | MetruyenScraper started
2025-07-07 10:30:19 | INFO | MetruyenScraper started
2025-07-07 10:30:19 | INFO | MetruyenScraper started
2025-07-07 10:30:19 | INFO | MetruyenScraper started
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:19 | INFO | Configuration loaded from config.yaml
2025-07-07 10:30:20 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:20 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:20 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:20 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:20 | INFO | Scraper engine initialized successfully
2025-07-07 10:30:20 | INFO | MetruyenScraper started
2025-07-07 10:30:20 | INFO | MetruyenScraper started
2025-07-07 10:30:20 | INFO | MetruyenScraper started
2025-07-07 10:30:20 | INFO | MetruyenScraper started
2025-07-07 10:30:20 | INFO | MetruyenScraper started
2025-07-07 10:30:25 | INFO | Scraper engine closed successfully
2025-07-07 10:30:25 | INFO | Scraper engine closed successfully
2025-07-07 10:30:25 | INFO | Scraper engine closed successfully
2025-07-07 10:30:25 | INFO | Scraper engine closed successfully
2025-07-07 10:30:25 | INFO | Scraper engine closed successfully
2025-07-07 10:30:25 | INFO | MetruyenScraper closed
2025-07-07 10:30:25 | INFO | MetruyenScraper closed
2025-07-07 10:30:25 | INFO | MetruyenScraper closed
2025-07-07 10:30:25 | INFO | MetruyenScraper closed
2025-07-07 10:30:25 | INFO | MetruyenScraper closed
2025-07-07 10:31:32 | INFO | Scraper engine initialized successfully
2025-07-07 10:31:32 | INFO | MetruyenScraper started
2025-07-07 10:31:32 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:31:35 | INFO | 🔍 Waiting for content with selectors: {'title': "h1, .title, [class*='title']", 'content': '#chapter-content', 'chapter_nav': "a[href*='chuong']", 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content'}
2025-07-07 10:31:35 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:31:45 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:31:45 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:31:45 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 24
2025-07-07 10:31:45 | INFO | 📊 Document ready state: complete
2025-07-07 10:31:45 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:31:45 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:31:45 | INFO |     Found 1 elements
2025-07-07 10:31:45 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:31:45 | INFO |         Text preview: ''
2025-07-07 10:31:45 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:31:45 | INFO |     Found 0 locked content indicators
2025-07-07 10:31:45 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751859105.png
2025-07-07 10:31:45 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:31:49 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:31:49 | INFO | 🔒 Checking for locked content...
2025-07-07 10:31:49 | INFO | 🔒 Locked content detected: False
2025-07-07 10:31:49 | INFO | 📝 Extracting main content...
2025-07-07 10:31:49 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:31:49 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:31:49 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:31:49 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:31:49 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:31:49 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:31:49 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:31:49 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:31:49 | INFO | 🔄 Found content with main: 1023 chars
2025-07-07 10:31:49 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:31:49 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:31:49 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:31:49 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:31:49 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:31:49 | INFO | 🔄 JS extraction found content: 7398 chars with body
2025-07-07 10:31:49 | INFO | 🔄 JS content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:31:49 | INFO | ✅ JavaScript content extraction successful
2025-07-07 10:31:49 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:31:49 | INFO | Scraper engine closed successfully
2025-07-07 10:31:49 | INFO | MetruyenScraper closed
2025-07-07 10:42:18 | INFO | 🔐 Performing authentication...
2025-07-07 10:42:19 | ERROR | No access token in response
2025-07-07 10:42:19 | WARNING | Authentication failed, continuing without auth
2025-07-07 10:42:19 | INFO | Scraper engine initialized successfully
2025-07-07 10:42:19 | INFO | MetruyenScraper started
2025-07-07 10:42:19 | INFO | Scraper engine closed successfully
2025-07-07 10:42:19 | INFO | MetruyenScraper closed
2025-07-07 10:42:47 | INFO | 🔐 Performing authentication...
2025-07-07 10:42:47 | INFO | ✅ Authentication successful
2025-07-07 10:42:48 | INFO | 🔐 Authentication headers added to browser context
2025-07-07 10:42:48 | INFO | 🔐 Authentication cookies added to browser context
2025-07-07 10:42:48 | INFO | Scraper engine initialized successfully
2025-07-07 10:42:48 | INFO | MetruyenScraper started
2025-07-07 10:42:48 | INFO | Scraper engine closed successfully
2025-07-07 10:42:48 | INFO | MetruyenScraper closed
2025-07-07 10:42:59 | INFO | 🔐 Authentication headers added to browser context
2025-07-07 10:42:59 | INFO | 🔐 Authentication cookies added to browser context
2025-07-07 10:42:59 | INFO | Scraper engine initialized successfully
2025-07-07 10:42:59 | INFO | MetruyenScraper started
2025-07-07 10:42:59 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:43:04 | INFO | 🔍 Waiting for content with selectors: {'chapter_nav': "a[href*='chuong']", 'content': '#chapter-content', 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content', 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'title': "h1, .title, [class*='title']"}
2025-07-07 10:43:04 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:43:14 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:43:14 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:43:14 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 24
2025-07-07 10:43:14 | INFO | 📊 Document ready state: complete
2025-07-07 10:43:14 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:43:14 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:43:14 | INFO |     Found 1 elements
2025-07-07 10:43:14 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:43:14 | INFO |         Text preview: ''
2025-07-07 10:43:14 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:43:14 | INFO |     Found 0 locked content indicators
2025-07-07 10:43:14 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751859794.png
2025-07-07 10:43:14 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:43:18 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:43:18 | INFO | 🔒 Checking for locked content...
2025-07-07 10:43:18 | INFO | 🔒 Locked content detected: False
2025-07-07 10:43:18 | INFO | 📝 Extracting main content...
2025-07-07 10:43:18 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:43:18 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:43:18 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:43:18 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:43:18 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:43:18 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:43:18 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:43:18 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:43:18 | INFO | 🔄 Found content with main: 1023 chars
2025-07-07 10:43:18 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:43:18 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:43:18 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:43:18 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:43:18 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:43:18 | INFO | 🔄 JS extraction found content: 7398 chars with body
2025-07-07 10:43:18 | INFO | 🔄 JS content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:43:18 | INFO | ✅ JavaScript content extraction successful
2025-07-07 10:43:18 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:43:21 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:43:22 | INFO | 🔍 Waiting for content with selectors: {'chapter_nav': "a[href*='chuong']", 'content': '#chapter-content', 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content', 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'title': "h1, .title, [class*='title']"}
2025-07-07 10:43:22 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:43:32 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:43:32 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:43:32 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 1
2025-07-07 10:43:32 | INFO | 📊 Document ready state: complete
2025-07-07 10:43:32 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:43:32 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:43:32 | INFO |     Found 1 elements
2025-07-07 10:43:32 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:43:32 | INFO |         Text preview: ''
2025-07-07 10:43:32 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:43:32 | INFO |     Found 0 locked content indicators
2025-07-07 10:43:32 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751859812.png
2025-07-07 10:43:32 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:43:36 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:43:36 | INFO | 🔒 Checking for locked content...
2025-07-07 10:43:36 | INFO | 🔒 Locked content detected: False
2025-07-07 10:43:36 | INFO | 📝 Extracting main content...
2025-07-07 10:43:36 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:43:36 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:43:36 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:43:36 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:43:36 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:43:36 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:43:36 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:43:36 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:43:36 | INFO | 🔄 Found content with main: 1019 chars
2025-07-07 10:43:36 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 01: Trùng sinh bắt đầu, bắt cóc vị hôn thê Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'chapte"
2025-07-07 10:43:36 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:43:36 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:43:36 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:43:36 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:43:36 | INFO | 🔄 JS extraction found content: 7299 chars with body
2025-07-07 10:43:36 | INFO | 🔄 JS content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 01: Trùng sinh bắt đầu, bắt cóc vị hôn thê Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'chapte"
2025-07-07 10:43:36 | INFO | ✅ JavaScript content extraction successful
2025-07-07 10:43:36 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-1
2025-07-07 10:43:36 | INFO | Scraper engine closed successfully
2025-07-07 10:43:36 | INFO | MetruyenScraper closed
2025-07-07 10:43:50 | INFO | 🔐 Authentication headers added to browser context
2025-07-07 10:43:50 | INFO | 🔐 Authentication cookies added to browser context
2025-07-07 10:43:50 | INFO | Scraper engine initialized successfully
2025-07-07 10:43:50 | INFO | MetruyenScraper started
2025-07-07 10:43:50 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:43:53 | INFO | 🔍 Waiting for content with selectors: {'chapter_nav': "a[href*='chuong']", 'content': '#chapter-content', 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content', 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'title': "h1, .title, [class*='title']"}
2025-07-07 10:43:53 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:44:03 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:44:03 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:44:03 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 24
2025-07-07 10:44:03 | INFO | 📊 Document ready state: complete
2025-07-07 10:44:03 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:44:03 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:44:03 | INFO |     Found 1 elements
2025-07-07 10:44:03 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:44:03 | INFO |         Text preview: ''
2025-07-07 10:44:03 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:44:03 | INFO |     Found 0 locked content indicators
2025-07-07 10:44:03 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751859843.png
2025-07-07 10:44:03 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:44:07 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:44:07 | INFO | 🔒 Checking for locked content...
2025-07-07 10:44:07 | INFO | 🔒 Locked content detected: False
2025-07-07 10:44:07 | INFO | 📝 Extracting main content...
2025-07-07 10:44:07 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:44:07 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:44:07 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:44:07 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:44:07 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:44:07 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:44:07 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:44:07 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:44:07 | INFO | 🔄 Found content with main: 1023 chars
2025-07-07 10:44:07 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:44:07 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:44:07 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:44:07 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:44:07 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:44:07 | INFO | 🔄 JS extraction found content: 7398 chars with body
2025-07-07 10:44:07 | INFO | 🔄 JS content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:44:07 | INFO | ✅ JavaScript content extraction successful
2025-07-07 10:44:07 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:44:07 | INFO | Data exported to json: output\scraped_data_20250707_104407.json
2025-07-07 10:44:07 | INFO | Data exported to csv: output\scraped_data_20250707_104407.csv
2025-07-07 10:44:07 | INFO | Scraper engine closed successfully
2025-07-07 10:44:07 | INFO | MetruyenScraper closed
2025-07-07 10:49:48 | INFO | 🔐 Authentication headers added to browser context
2025-07-07 10:49:48 | INFO | 🔐 Authentication cookies added to browser context
2025-07-07 10:49:48 | INFO | Scraper engine initialized successfully
2025-07-07 10:49:48 | INFO | MetruyenScraper started
2025-07-07 10:49:48 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:49:50 | INFO | 🔍 Waiting for content with selectors: {'chapter_nav': "a[href*='chuong']", 'content': '#chapter-content', 'locked_content': '.chapter-locked, [data-locked], .vip-content, .premium-content', 'next_chapter': "button[data-x-ref='nextId']", 'prev_chapter': "button[data-x-ref='prevId']", 'title': "h1, .title, [class*='title']"}
2025-07-07 10:49:50 | INFO | ⏳ Waiting for content selector: #chapter-content
2025-07-07 10:50:00 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:50:00 | INFO | 🔍 DEBUG: Analyzing page structure...
2025-07-07 10:50:00 | INFO | 📄 Page title: Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập - Chương 24
2025-07-07 10:50:00 | INFO | 📊 Document ready state: complete
2025-07-07 10:50:00 | INFO | 🔍 Searching for content with selector: #chapter-content
2025-07-07 10:50:00 | INFO |   Testing selector 1: #chapter-content
2025-07-07 10:50:00 | INFO |     Found 1 elements
2025-07-07 10:50:00 | INFO |       Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:50:00 | INFO |         Text preview: ''
2025-07-07 10:50:00 | INFO | 🔒 Checking for locked content with selector: .chapter-locked, [data-locked], .vip-content, .premium-content
2025-07-07 10:50:00 | INFO |     Found 0 locked content indicators
2025-07-07 10:50:00 | INFO | 📸 Debug screenshot saved: debug_screenshot_1751860200.png
2025-07-07 10:50:00 | INFO | 🔧 Applying metruyencv.com specific content loading...
2025-07-07 10:50:04 | INFO | 🔧 Metruyencv.com content loading handling completed
2025-07-07 10:50:04 | INFO | 🔒 Checking for locked content...
2025-07-07 10:50:04 | INFO | 🔒 Locked content detected: False
2025-07-07 10:50:04 | INFO | 📝 Extracting main content...
2025-07-07 10:50:04 | INFO | 📝 Using content selector: #chapter-content
2025-07-07 10:50:04 | INFO | 📝 Trying selector 1: #chapter-content
2025-07-07 10:50:04 | INFO | 📝 Found 1 elements with selector: #chapter-content
2025-07-07 10:50:04 | INFO | 📝   Element 1: <DIV> class='break-words' id='chapter-content' visible=False
2025-07-07 10:50:04 | INFO | 📝   inner_text: empty or whitespace only
2025-07-07 10:50:04 | INFO | 📝   text_content: empty or whitespace only
2025-07-07 10:50:04 | INFO | 📝   inner_html: empty or whitespace only
2025-07-07 10:50:04 | WARNING | 📝 No content extracted from any selector
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative content extraction methods...
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: #chapter-content
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: .chapter-content
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: .content
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: [id*='content']
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: [class*='content']
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: .reading-content
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: .chapter-text
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: .story-content
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: div[id*='chapter']
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: div[class*='chapter']
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: .break-words
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: main
2025-07-07 10:50:04 | INFO | 🔄 Found content with main: 1023 chars
2025-07-07 10:50:04 | INFO | 🔄 Content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:50:04 | INFO | 🔄 Trying alternative selector: article
2025-07-07 10:50:04 | INFO | 🔄 Trying JavaScript content extraction...
2025-07-07 10:50:04 | INFO | 🔄 Executing JavaScript method 1
2025-07-07 10:50:04 | INFO | 🔄 Executing JavaScript method 2
2025-07-07 10:50:04 | INFO | 🔄 JS extraction found content: 7398 chars with body
2025-07-07 10:50:04 | INFO | 🔄 JS content preview: "Phản Phái Tiếng Lòng Bị Nữ Chính Nghe Lén Về Sau, Nội Dung Cốt Truyện Sập Mang Quả Pasy  Chương 24: Nghe lén tiếng lòng, nghịch thiên cải mệnh Cấu hìnhMục lụcĐánh dấu const listMenuTabs = [ { key: 'ch"
2025-07-07 10:50:04 | INFO | ✅ JavaScript content extraction successful
2025-07-07 10:50:04 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:50:04 | INFO | Data exported to json: output\scraped_data_20250707_105004.json
2025-07-07 10:50:04 | INFO | Data exported to csv: output\scraped_data_20250707_105004.csv
2025-07-07 10:50:04 | INFO | Scraper engine closed successfully
2025-07-07 10:50:04 | INFO | MetruyenScraper closed
