2025-07-07 10:07:16 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:16 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:16 | INFO | MetruyenScraper initialized
2025-07-07 10:07:16 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | MetruyenScraper initialized
2025-07-07 10:07:17 | INFO | Metruyen<PERSON>craper initialized
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | MetruyenScraper initialized
2025-07-07 10:07:17 | INFO | MetruyenScraper initialized
2025-07-07 10:07:17 | INFO | MetruyenScraper initialized
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:17 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:18 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:18 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:18 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:18 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() got an unexpected keyword argument 'width'
2025-07-07 10:07:39 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | INFO | MetruyenScraper initialized
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:39 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | INFO | MetruyenScraper initialized
2025-07-07 10:07:39 | INFO | MetruyenScraper initialized
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:39 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | MetruyenScraper initialized
2025-07-07 10:07:40 | INFO | MetruyenScraper initialized
2025-07-07 10:07:40 | INFO | MetruyenScraper initialized
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | INFO | Configuration loaded from config.yaml
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:07:40 | ERROR | Failed to initialize scraper engine: Page.set_viewport_size() takes 2 positional arguments but 3 were given
2025-07-07 10:08:51 | INFO | Scraper engine initialized successfully
2025-07-07 10:08:51 | INFO | MetruyenScraper started
2025-07-07 10:08:51 | INFO | Configuration validation: PASSED
2025-07-07 10:08:51 | INFO | Scraper engine closed successfully
2025-07-07 10:08:51 | INFO | MetruyenScraper closed
2025-07-07 10:09:22 | INFO | Scraper engine initialized successfully
2025-07-07 10:09:22 | INFO | MetruyenScraper started
2025-07-07 10:09:22 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:09:22 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:09:37 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator(".content, .chapter-content, [class*='content'], #chapter-content") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:09:37 | ERROR | Data extraction failed: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | ERROR | Failed to scrape https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | WARNING | Attempt 1/3 failed: Unknown error: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | ERROR | Non-retryable error: Unknown error: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | ERROR | Failed to scrape https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24: Unknown error: Page.query_selector: SyntaxError: Failed to execute 'querySelectorAll' on 'Document': 'a:contains("Chương sau")' is not a valid selector.
    at query (<anonymous>:4989:41)
    at <anonymous>:4999:7
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl._queryCSS (<anonymous>:4986:17)
    at SelectorEvaluatorImpl._querySimple (<anonymous>:4866:19)
    at <anonymous>:4814:29
    at SelectorEvaluatorImpl._cached (<anonymous>:4776:20)
    at SelectorEvaluatorImpl.query (<anonymous>:4807:19)
    at Object.query (<anonymous>:5021:44)
    at <anonymous>:4979:21
2025-07-07 10:09:37 | WARNING | Test scraping returned no data
2025-07-07 10:09:37 | INFO | Scraper engine closed successfully
2025-07-07 10:09:37 | INFO | MetruyenScraper closed
2025-07-07 10:10:38 | INFO | Scraper engine initialized successfully
2025-07-07 10:10:38 | INFO | MetruyenScraper started
2025-07-07 10:10:38 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:10:38 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:10:50 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:10:50 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:10:50 | INFO | Test scraping successful
2025-07-07 10:10:50 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:01 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:01 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:01 | INFO | Data exported to json: test_output\scraped_data_20250707_101101.json
2025-07-07 10:11:01 | INFO | Data exported to csv: test_output\scraped_data_20250707_101101.csv
2025-07-07 10:11:01 | INFO | Scraper engine closed successfully
2025-07-07 10:11:01 | INFO | MetruyenScraper closed
2025-07-07 10:11:21 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:21 | INFO | MetruyenScraper started
2025-07-07 10:11:21 | INFO | Configuration validation: PASSED
2025-07-07 10:11:21 | INFO | Scraper engine closed successfully
2025-07-07 10:11:21 | INFO | MetruyenScraper closed
2025-07-07 10:11:21 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:21 | INFO | MetruyenScraper initialized
2025-07-07 10:11:21 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:22 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:22 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:22 | INFO | MetruyenScraper started
2025-07-07 10:11:22 | INFO | MetruyenScraper started
2025-07-07 10:11:22 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:22 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:22 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:22 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:33 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:33 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:33 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:33 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:33 | INFO | Test scraping successful
2025-07-07 10:11:33 | INFO | Test scraping successful
2025-07-07 10:11:33 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:33 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:45 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:45 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:11:45 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:45 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:45 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:45 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:45 | INFO | MetruyenScraper started
2025-07-07 10:11:45 | INFO | MetruyenScraper started
2025-07-07 10:11:45 | INFO | MetruyenScraper started
2025-07-07 10:11:45 | INFO | Data exported to json: test_output\scraped_data_20250707_101145.json
2025-07-07 10:11:45 | INFO | Data exported to json: test_output\scraped_data_20250707_101145.json
2025-07-07 10:11:45 | INFO | Data exported to json: test_output\scraped_data_20250707_101145.json
2025-07-07 10:11:45 | INFO | Data exported to csv: test_output\scraped_data_20250707_101145.csv
2025-07-07 10:11:45 | INFO | Data exported to csv: test_output\scraped_data_20250707_101145.csv
2025-07-07 10:11:45 | INFO | Data exported to csv: test_output\scraped_data_20250707_101145.csv
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | Scraper engine closed successfully
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | MetruyenScraper closed
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | MetruyenScraper initialized
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:45 | INFO | Configuration loaded from config.yaml
2025-07-07 10:11:46 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:46 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:46 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:46 | INFO | Scraper engine initialized successfully
2025-07-07 10:11:46 | INFO | MetruyenScraper started
2025-07-07 10:11:46 | INFO | MetruyenScraper started
2025-07-07 10:11:46 | INFO | MetruyenScraper started
2025-07-07 10:11:46 | INFO | MetruyenScraper started
2025-07-07 10:11:46 | INFO | Starting to scrape 2 URLs
2025-07-07 10:11:46 | INFO | Starting to scrape 2 URLs
2025-07-07 10:11:46 | INFO | Starting to scrape 2 URLs
2025-07-07 10:11:46 | INFO | Starting to scrape 2 URLs
2025-07-07 10:11:46 | INFO | Scraping URL: https://metruyencv.com/invalid-page-404
2025-07-07 10:11:46 | INFO | Scraping URL: https://metruyencv.com/invalid-page-404
2025-07-07 10:11:46 | INFO | Scraping URL: https://metruyencv.com/invalid-page-404
2025-07-07 10:11:46 | INFO | Scraping URL: https://metruyencv.com/invalid-page-404
2025-07-07 10:11:46 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:46 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:46 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:46 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Failed to load page: HTTP 404
2025-07-07 10:11:46 | WARNING | Attempt 1/3 failed: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | WARNING | Attempt 1/3 failed: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | WARNING | Attempt 1/3 failed: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | WARNING | Attempt 1/3 failed: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Non-retryable error: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Non-retryable error: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Non-retryable error: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Non-retryable error: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:46 | ERROR | Failed to scrape https://metruyencv.com/invalid-page-404: Unknown error: Failed to load page: HTTP 404
2025-07-07 10:11:49 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | WARNING | Attempt 1/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | WARNING | Attempt 1/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | WARNING | Attempt 1/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | WARNING | Attempt 1/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:49 | INFO | Retrying in 0.989 seconds...
2025-07-07 10:11:49 | INFO | Retrying in 0.989 seconds...
2025-07-07 10:11:49 | INFO | Retrying in 0.989 seconds...
2025-07-07 10:11:49 | INFO | Retrying in 0.989 seconds...
2025-07-07 10:11:50 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:50 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:50 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:50 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:52 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | WARNING | Attempt 2/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | WARNING | Attempt 2/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | WARNING | Attempt 2/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | WARNING | Attempt 2/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:52 | INFO | Retrying in 2.086 seconds...
2025-07-07 10:11:52 | INFO | Retrying in 2.086 seconds...
2025-07-07 10:11:52 | INFO | Retrying in 2.086 seconds...
2025-07-07 10:11:52 | INFO | Retrying in 2.086 seconds...
2025-07-07 10:11:54 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:54 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:54 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:54 | INFO | Scraping URL: https://invalid-domain-that-does-not-exist.com/page
2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Attempt 3/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Attempt 3/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Attempt 3/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Attempt 3/3 failed: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | All 3 attempts failed
2025-07-07 10:11:55 | ERROR | All 3 attempts failed
2025-07-07 10:11:55 | ERROR | All 3 attempts failed
2025-07-07 10:11:55 | ERROR | All 3 attempts failed
2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | ERROR | Failed to scrape https://invalid-domain-that-does-not-exist.com/page: Network error: Page.goto: net::ERR_NAME_NOT_RESOLVED at https://invalid-domain-that-does-not-exist.com/page
Call log:
  - navigating to "https://invalid-domain-that-does-not-exist.com/page", waiting until "networkidle"

2025-07-07 10:11:55 | INFO | Successfully scraped 0/2 URLs
2025-07-07 10:11:55 | INFO | Successfully scraped 0/2 URLs
2025-07-07 10:11:55 | INFO | Successfully scraped 0/2 URLs
2025-07-07 10:11:55 | INFO | Successfully scraped 0/2 URLs
2025-07-07 10:11:55 | INFO | Scraper engine closed successfully
2025-07-07 10:11:55 | INFO | Scraper engine closed successfully
2025-07-07 10:11:55 | INFO | Scraper engine closed successfully
2025-07-07 10:11:55 | INFO | Scraper engine closed successfully
2025-07-07 10:11:55 | INFO | MetruyenScraper closed
2025-07-07 10:11:55 | INFO | MetruyenScraper closed
2025-07-07 10:11:55 | INFO | MetruyenScraper closed
2025-07-07 10:11:55 | INFO | MetruyenScraper closed
2025-07-07 10:12:05 | INFO | Scraper engine initialized successfully
2025-07-07 10:12:05 | INFO | MetruyenScraper started
2025-07-07 10:12:05 | INFO | Configuration validation: PASSED
2025-07-07 10:12:05 | INFO | Testing scraping on: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:12:05 | INFO | Scraping URL: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:12:17 | WARNING | Content wait timeout: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("#chapter-content, .content, .chapter-content, [class*='content']") to be visible
    24 × locator resolved to hidden <div class="break-words" id="chapter-content"></div>

2025-07-07 10:12:17 | INFO | Successfully scraped: https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24
2025-07-07 10:12:17 | INFO | Test scraping successful
2025-07-07 10:12:17 | INFO | Scraper engine closed successfully
2025-07-07 10:12:17 | INFO | MetruyenScraper closed
