#!/usr/bin/env python3
"""
Setup script for MetruyenScraper
Installs dependencies and sets up the environment
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"  Command: {command}")
        print(f"  Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print(f"✗ Python 3.8+ required, but you have {sys.version}")
        return False
    
    print(f"✓ Python {sys.version.split()[0]} is compatible")
    return True


def install_requirements():
    """Install Python requirements"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("✗ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )


def install_playwright():
    """Install Playwright browsers"""
    return run_command(
        f"{sys.executable} -m playwright install chromium",
        "Installing Playwright browsers"
    )


def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = ["output", "logs", "screenshots"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    return True


def test_installation():
    """Test the installation"""
    print("🧪 Testing installation...")
    
    try:
        # Test imports
        sys.path.append(str(Path("src")))
        from src.config_manager import ConfigManager
        from src.metruyenscraper import MetruyenScraper
        
        print("✓ All modules imported successfully")
        
        # Test configuration
        config = ConfigManager()
        print("✓ Configuration loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Installation test failed: {e}")
        return False


def main():
    """Main setup function"""
    print("MetruyenScraper Setup")
    print("=" * 50)
    
    steps = [
        ("Check Python version", check_python_version),
        ("Install Python dependencies", install_requirements),
        ("Install Playwright browsers", install_playwright),
        ("Create directories", create_directories),
        ("Test installation", test_installation)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        
        if not step_func():
            failed_steps.append(step_name)
            print(f"⚠️  Step '{step_name}' failed but continuing...")
    
    print(f"\n{'='*50}")
    print("Setup Summary")
    print("=" * 50)
    
    if not failed_steps:
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Review and modify config.yaml if needed")
        print("2. Run tests: python test_scraper.py")
        print("3. Try examples: python example_usage.py")
        print("4. Use CLI: python run_scraper.py --help")
        return True
    else:
        print(f"⚠️  Setup completed with {len(failed_steps)} issues:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nYou may need to resolve these issues manually.")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Setup failed: {e}")
        sys.exit(1)
