import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.metruyenscraper import MetruyenScraper

async def test_initialization():
    """Test basic scraper initialization"""
    try:
        async with MetruyenScraper() as scraper:
            print("✓ Scraper initialized successfully")
            
            # Test configuration validation
            validation = scraper.validate_configuration()
            print(f"✓ Configuration valid: {validation['valid']}")
            
            return True
    except Exception as e:
        print(f"✗ Initialization failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_initialization())
    print(f"Test result: {'PASS' if success else 'FAIL'}")
