"""
Data Processing and Export Module
Handles data validation, cleaning, and export to multiple formats
"""

import json
import csv
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from loguru import logger


class DataProcessor:
    """Processes and exports scraped data"""
    
    def __init__(self, config_manager):
        self.config = config_manager
        self.output_config = self.config.get_output_config()
        self.data_cache: List[Dict[str, Any]] = []
    
    def add_data(self, data: Dict[str, Any]) -> None:
        """Add scraped data to cache"""
        # Validate and clean data
        cleaned_data = self._clean_data(data)
        validated_data = self._validate_data(cleaned_data)
        
        if validated_data:
            self.data_cache.append(validated_data)
            logger.debug(f"Data added to cache: {validated_data.get('url', 'Unknown URL')}")
    
    def _clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and normalize scraped data"""
        cleaned = data.copy()
        
        # Clean text content
        if 'content' in cleaned and cleaned['content']:
            cleaned['content'] = self._clean_text(cleaned['content'])
        
        if 'title' in cleaned and cleaned['title']:
            cleaned['title'] = self._clean_text(cleaned['title'])
        
        # Add processing timestamp
        cleaned['processed_at'] = datetime.now().isoformat()
        
        # Normalize URL
        if 'url' in cleaned:
            cleaned['url'] = cleaned['url'].strip()
        
        return cleaned
    
    def _clean_text(self, text: str) -> str:
        """Clean text content"""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        # Remove common unwanted characters
        text = text.replace('\u00a0', ' ')  # Non-breaking space
        text = text.replace('\u200b', '')   # Zero-width space
        
        return text.strip()
    
    def _validate_data(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Validate scraped data"""
        required_fields = ['url', 'timestamp']
        
        # Check required fields
        for field in required_fields:
            if field not in data or data[field] is None:
                logger.warning(f"Missing required field '{field}' in data")
                return None
        
        # Validate URL format
        if not self._is_valid_url(data['url']):
            logger.warning(f"Invalid URL format: {data['url']}")
            return None
        
        return data
    
    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format"""
        try:
            from urllib.parse import urlparse
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def export_data(self, output_dir: str = "output") -> Dict[str, str]:
        """Export cached data to configured formats"""
        if not self.data_cache:
            logger.warning("No data to export")
            return {}
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        exported_files = {}
        formats = self.output_config.get('formats', ['json'])
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for format_type in formats:
            try:
                if format_type == 'json':
                    file_path = self._export_json(output_path, timestamp)
                elif format_type == 'csv':
                    file_path = self._export_csv(output_path, timestamp)
                elif format_type == 'excel':
                    file_path = self._export_excel(output_path, timestamp)
                else:
                    logger.warning(f"Unsupported export format: {format_type}")
                    continue
                
                exported_files[format_type] = str(file_path)
                logger.info(f"Data exported to {format_type}: {file_path}")
                
            except Exception as e:
                logger.error(f"Failed to export {format_type}: {e}")
        
        return exported_files
    
    def _export_json(self, output_path: Path, timestamp: str) -> Path:
        """Export data to JSON format"""
        json_config = self.output_config.get('json', {})
        pretty = json_config.get('pretty', True)
        encoding = json_config.get('encoding', 'utf-8')
        
        file_path = output_path / f"scraped_data_{timestamp}.json"
        
        with open(file_path, 'w', encoding=encoding) as f:
            if pretty:
                json.dump(self.data_cache, f, indent=2, ensure_ascii=False)
            else:
                json.dump(self.data_cache, f, ensure_ascii=False)
        
        return file_path
    
    def _export_csv(self, output_path: Path, timestamp: str) -> Path:
        """Export data to CSV format"""
        csv_config = self.output_config.get('csv', {})
        encoding = csv_config.get('encoding', 'utf-8-sig')
        delimiter = csv_config.get('delimiter', ',')
        
        file_path = output_path / f"scraped_data_{timestamp}.csv"
        
        # Flatten nested data for CSV
        flattened_data = []
        for item in self.data_cache:
            flat_item = self._flatten_dict(item)
            flattened_data.append(flat_item)
        
        if flattened_data:
            df = pd.DataFrame(flattened_data)
            df.to_csv(file_path, index=False, encoding=encoding, sep=delimiter)
        
        return file_path
    
    def _export_excel(self, output_path: Path, timestamp: str) -> Path:
        """Export data to Excel format"""
        file_path = output_path / f"scraped_data_{timestamp}.xlsx"
        
        # Flatten nested data for Excel
        flattened_data = []
        for item in self.data_cache:
            flat_item = self._flatten_dict(item)
            flattened_data.append(flat_item)
        
        if flattened_data:
            df = pd.DataFrame(flattened_data)
            df.to_excel(file_path, index=False, engine='openpyxl')
        
        return file_path
    
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = '', sep: str = '_') -> Dict[str, Any]:
        """Flatten nested dictionary"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # Convert list to string representation
                items.append((new_key, str(v)))
            else:
                items.append((new_key, v))
        
        return dict(items)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about cached data"""
        if not self.data_cache:
            return {'total_items': 0}
        
        stats = {
            'total_items': len(self.data_cache),
            'locked_content': sum(1 for item in self.data_cache if item.get('is_locked', False)),
            'with_content': sum(1 for item in self.data_cache if item.get('content')),
            'unique_urls': len(set(item['url'] for item in self.data_cache)),
            'date_range': {
                'earliest': min(item['timestamp'] for item in self.data_cache),
                'latest': max(item['timestamp'] for item in self.data_cache)
            }
        }
        
        return stats
    
    def clear_cache(self) -> None:
        """Clear cached data"""
        self.data_cache.clear()
        logger.info("Data cache cleared")
    
    def filter_data(self, filter_func) -> List[Dict[str, Any]]:
        """Filter cached data using custom function"""
        return [item for item in self.data_cache if filter_func(item)]
    
    def get_data_by_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Get data for specific URL"""
        for item in self.data_cache:
            if item.get('url') == url:
                return item
        return None
