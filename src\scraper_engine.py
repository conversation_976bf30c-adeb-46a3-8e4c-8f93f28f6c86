"""
Core Scraper Engine using <PERSON>wright for dynamic content handling
Supports anti-detection measures and robust error handling
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin, urlparse
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from fake_useragent import UserAgent
from loguru import logger

from .config_manager import ConfigManager
from .anti_detection import AntiDetectionManager
from .data_processor import DataProcessor


class ScraperEngine:
    """Main scraper engine with Playwright browser automation"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config = ConfigManager(config_path)
        self.anti_detection = AntiDetectionManager(self.config)
        self.data_processor = DataProcessor(self.config)
        
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        log_level = log_config.get('level', 'INFO')
        log_file = log_config.get('file', 'scraper.log')
        log_format = log_config.get('format', '{time} | {level} | {message}')
        
        logger.add(log_file, level=log_level, format=log_format, rotation="10 MB")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def start(self) -> None:
        """Initialize browser and context"""
        try:
            playwright = await async_playwright().start()
            
            browser_config = self.config.get('scraper.browser', {})
            
            # Launch browser with stealth settings
            self.browser = await playwright.chromium.launch(
                headless=browser_config.get('headless', True),
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-images',  # Speed optimization
                    '--disable-javascript-harmony-shipping',
                    '--disable-background-timer-throttling',
                    '--disable-renderer-backgrounding',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-ipc-flooding-protection',
                ]
            )
            
            # Create context with anti-detection settings
            context_options = await self.anti_detection.get_context_options()
            self.context = await self.browser.new_context(**context_options)
            
            # Apply stealth scripts
            await self.anti_detection.apply_stealth_scripts(self.context)
            
            # Create page
            self.page = await self.context.new_page()
            
            # Viewport is set in context options, no need to set it again on page
            
            logger.info("Scraper engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize scraper engine: {e}")
            raise
    
    async def close(self) -> None:
        """Close browser and cleanup resources"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            
            logger.info("Scraper engine closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing scraper engine: {e}")
    
    async def scrape_url(self, url: str, target_name: str = "metruyencv") -> Dict[str, Any]:
        """Scrape a single URL and extract data"""
        if not self.page:
            raise RuntimeError("Scraper engine not initialized. Use async context manager.")
        
        try:
            logger.info(f"Scraping URL: {url}")
            
            # Apply anti-detection measures
            await self.anti_detection.randomize_user_agent(self.page)
            await self.anti_detection.add_random_delay()
            
            # Navigate to URL
            timeout = self.config.get('scraper.browser.timeout', 30000)
            response = await self.page.goto(url, timeout=timeout, wait_until='networkidle')
            
            if not response or response.status >= 400:
                raise Exception(f"Failed to load page: HTTP {response.status if response else 'No response'}")
            
            # Wait for dynamic content
            await self._wait_for_content(target_name)
            
            # Extract data
            data = await self._extract_data(url, target_name)
            
            logger.info(f"Successfully scraped: {url}")
            return data
            
        except Exception as e:
            logger.error(f"Failed to scrape {url}: {e}")
            raise
    
    async def _wait_for_content(self, target_name: str) -> None:
        """Wait for dynamic content to load"""
        target_config = self.config.get_target_config(target_name)
        wait_conditions = target_config.get('wait_conditions', ['networkidle'])
        
        # Wait for specific selectors if configured
        selectors = self.config.get_selectors(target_name)
        
        try:
            # Wait for main content selector
            if 'content' in selectors:
                await self.page.wait_for_selector(selectors['content'], timeout=10000)
            
            # Additional wait for page stability
            delays = self.config.get_delays()
            page_load_delay = delays.get('page_load_delay', 3)
            await asyncio.sleep(page_load_delay)
            
        except Exception as e:
            logger.warning(f"Content wait timeout: {e}")
    
    async def _extract_data(self, url: str, target_name: str) -> Dict[str, Any]:
        """Extract data from current page"""
        selectors = self.config.get_selectors(target_name)
        data = {
            'url': url,
            'timestamp': time.time(),
            'title': None,
            'content': None,
            'metadata': {},
            'navigation': {},
            'is_locked': False
        }
        
        try:
            # Extract title
            if 'title' in selectors:
                title_element = await self.page.query_selector(selectors['title'])
                if title_element:
                    data['title'] = await title_element.inner_text()
            
            # Check if content is locked
            if 'locked_content' in selectors:
                locked_element = await self.page.query_selector(selectors['locked_content'])
                data['is_locked'] = locked_element is not None
            
            # Extract main content
            if 'content' in selectors and not data['is_locked']:
                content_element = await self.page.query_selector(selectors['content'])
                if content_element:
                    # Try to get text content, even if element is hidden
                    try:
                        data['content'] = await content_element.inner_text()
                    except Exception:
                        # If inner_text fails, try text_content
                        data['content'] = await content_element.text_content()
            
            # Extract navigation links
            await self._extract_navigation(data, selectors)
            
            # Extract metadata
            data['metadata'] = await self._extract_metadata()
            
            return data
            
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise
    
    async def _extract_navigation(self, data: Dict[str, Any], selectors: Dict[str, str]) -> None:
        """Extract navigation links"""
        nav_data = {}

        try:
            # Next chapter link - try multiple approaches
            if 'next_chapter' in selectors:
                try:
                    next_element = await self.page.query_selector(selectors['next_chapter'])
                    if next_element:
                        next_href = await next_element.get_attribute('href')
                        if next_href:
                            nav_data['next_chapter'] = urljoin(data['url'], next_href)
                except Exception:
                    # Fallback: look for any link containing "sau" or "next"
                    next_elements = await self.page.query_selector_all('a[href*="chuong"]')
                    for element in next_elements:
                        text = await element.inner_text()
                        if 'sau' in text.lower() or 'next' in text.lower():
                            href = await element.get_attribute('href')
                            if href:
                                nav_data['next_chapter'] = urljoin(data['url'], href)
                                break

            # Previous chapter link - try multiple approaches
            if 'prev_chapter' in selectors:
                try:
                    prev_element = await self.page.query_selector(selectors['prev_chapter'])
                    if prev_element:
                        prev_href = await prev_element.get_attribute('href')
                        if prev_href:
                            nav_data['prev_chapter'] = urljoin(data['url'], prev_href)
                except Exception:
                    # Fallback: look for any link containing "trước" or "prev"
                    prev_elements = await self.page.query_selector_all('a[href*="chuong"]')
                    for element in prev_elements:
                        text = await element.inner_text()
                        if 'trước' in text.lower() or 'prev' in text.lower():
                            href = await element.get_attribute('href')
                            if href:
                                nav_data['prev_chapter'] = urljoin(data['url'], href)
                                break

            # All chapter links
            if 'chapter_nav' in selectors:
                chapter_elements = await self.page.query_selector_all(selectors['chapter_nav'])
                chapters = []
                for element in chapter_elements:
                    try:
                        href = await element.get_attribute('href')
                        text = await element.inner_text()
                        if href:
                            chapters.append({
                                'url': urljoin(data['url'], href),
                                'title': text.strip()
                            })
                    except Exception:
                        continue
                nav_data['chapters'] = chapters

        except Exception as e:
            logger.warning(f"Navigation extraction failed: {e}")

        data['navigation'] = nav_data
    
    async def _extract_metadata(self) -> Dict[str, Any]:
        """Extract page metadata"""
        metadata = {}
        
        try:
            # Page title
            title = await self.page.title()
            metadata['page_title'] = title
            
            # Meta description
            desc_element = await self.page.query_selector('meta[name="description"]')
            if desc_element:
                metadata['description'] = await desc_element.get_attribute('content')
            
            # Meta keywords
            keywords_element = await self.page.query_selector('meta[name="keywords"]')
            if keywords_element:
                metadata['keywords'] = await keywords_element.get_attribute('content')
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Metadata extraction failed: {e}")
            return metadata
