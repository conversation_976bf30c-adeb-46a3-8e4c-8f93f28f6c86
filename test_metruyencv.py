import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.metruyenscraper import MetruyenScraper

async def test_metruyencv_scraping():
    """Test scraping the actual metruyencv.com URL"""
    test_url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
    
    try:
        async with MetruyenScraper() as scraper:
            print(f"🔍 Testing site: {test_url}")
            
            # First test the site
            test_results = await scraper.test_target_site(test_url)
            print(f"✓ Site test completed")
            print(f"  Success: {test_results['success']}")
            print(f"  Data extracted: {test_results['data_extracted']}")
            print(f"  Content locked: {test_results['content_locked']}")
            print(f"  Navigation found: {test_results['navigation_found']}")
            
            if test_results['errors']:
                print(f"  Errors: {test_results['errors']}")
            
            if test_results['success']:
                print(f"\n🚀 Attempting to scrape URL...")
                data = await scraper.scrape_url(test_url)
                
                if data:
                    print(f"✓ Successfully scraped!")
                    print(f"  Title: {data.get('title', 'N/A')}")
                    print(f"  URL: {data.get('url', 'N/A')}")
                    print(f"  Content locked: {data.get('is_locked', False)}")
                    print(f"  Has content: {bool(data.get('content'))}")
                    if data.get('content'):
                        content_preview = data['content'][:200] + "..." if len(data['content']) > 200 else data['content']
                        print(f"  Content preview: {content_preview}")
                    
                    navigation = data.get('navigation', {})
                    print(f"  Navigation links found: {len(navigation.get('chapters', []))}")
                    if navigation.get('next_chapter'):
                        print(f"  Next chapter: {navigation['next_chapter']}")
                    if navigation.get('prev_chapter'):
                        print(f"  Previous chapter: {navigation['prev_chapter']}")
                    
                    # Export data
                    print(f"\n📁 Exporting data...")
                    exported_files = scraper.export_data("test_output")
                    for format_type, file_path in exported_files.items():
                        print(f"  {format_type.upper()}: {file_path}")
                    
                    # Get statistics
                    stats = scraper.get_statistics()
                    print(f"\n📊 Statistics:")
                    print(f"  Total items: {stats['data_statistics']['total_items']}")
                    print(f"  Locked content: {stats['data_statistics']['locked_content']}")
                    print(f"  Total errors: {stats['error_statistics']['total_errors']}")
                    
                    return True
                else:
                    print("✗ No data returned from scraping")
                    return False
            else:
                print("✗ Site test failed, cannot proceed with scraping")
                return False
                
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("MetruyenScraper - Real Website Test")
    print("=" * 50)
    
    success = asyncio.run(test_metruyencv_scraping())
    
    print("\n" + "=" * 50)
    print(f"Test result: {'✅ PASS' if success else '❌ FAIL'}")
    
    if success:
        print("\n🎉 The scraper is working correctly with metruyencv.com!")
        print("You can now use the scraper for your web scraping needs.")
    else:
        print("\n⚠️ The scraper encountered issues. Check the output above for details.")
