"""
Test script for MetruyenScraper
Tests basic functionality and configuration
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.metruyenscraper import MetruyenScraper
from src.config_manager import ConfigManager


async def test_configuration():
    """Test configuration loading and validation"""
    print("Testing configuration...")
    
    try:
        config = ConfigManager()
        
        # Test basic config loading
        scraper_config = config.get_scraper_config()
        print(f"✓ Scraper config loaded: {bool(scraper_config)}")
        
        # Test target config
        target_config = config.get_target_config('metruyencv')
        print(f"✓ Target config loaded: {bool(target_config)}")
        
        # Test selectors
        selectors = config.get_selectors('metruyencv')
        print(f"✓ Selectors loaded: {len(selectors)} selectors")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


async def test_scraper_initialization():
    """Test scraper initialization"""
    print("\nTesting scraper initialization...")
    
    try:
        async with MetruyenScraper() as scraper:
            # Test validation
            validation = scraper.validate_configuration()
            print(f"✓ Configuration valid: {validation['valid']}")
            
            if validation['errors']:
                print(f"  Errors: {validation['errors']}")
            if validation['warnings']:
                print(f"  Warnings: {validation['warnings']}")
            
            return validation['valid']
            
    except Exception as e:
        print(f"✗ Scraper initialization failed: {e}")
        return False


async def test_single_url_scraping():
    """Test scraping a single URL"""
    print("\nTesting single URL scraping...")
    
    test_url = "https://metruyencv.com/truyen/phan-phai-tieng-long-bi-nu-chinh-nghe-len-ve-sau-noi-dung-cot-truyen-sap/chuong-24"
    
    try:
        async with MetruyenScraper() as scraper:
            # First test the site
            test_results = await scraper.test_target_site(test_url)
            print(f"✓ Site test completed: {test_results['success']}")
            
            if not test_results['success']:
                print(f"  Errors: {test_results['errors']}")
                return False
            
            # Try scraping
            data = await scraper.scrape_url(test_url)
            
            if data:
                print(f"✓ URL scraped successfully")
                print(f"  Title: {data.get('title', 'N/A')[:50]}...")
                print(f"  Has content: {bool(data.get('content'))}")
                print(f"  Content locked: {data.get('is_locked', False)}")
                print(f"  Navigation links: {len(data.get('navigation', {}).get('chapters', []))}")
                return True
            else:
                print("✗ No data returned")
                return False
                
    except Exception as e:
        print(f"✗ Single URL scraping failed: {e}")
        return False


async def test_data_export():
    """Test data export functionality"""
    print("\nTesting data export...")
    
    try:
        async with MetruyenScraper() as scraper:
            # Add some test data
            test_data = {
                'url': 'https://example.com/test',
                'title': 'Test Title',
                'content': 'Test content',
                'timestamp': 1640995200.0,
                'is_locked': False
            }
            
            scraper.data_processor.add_data(test_data)
            
            # Test export
            exported_files = scraper.export_data("test_output")
            print(f"✓ Data exported: {len(exported_files)} formats")
            
            for format_type, file_path in exported_files.items():
                if Path(file_path).exists():
                    print(f"  ✓ {format_type.upper()}: {file_path}")
                else:
                    print(f"  ✗ {format_type.upper()}: File not created")
            
            return len(exported_files) > 0
            
    except Exception as e:
        print(f"✗ Data export test failed: {e}")
        return False


async def test_error_handling():
    """Test error handling with invalid URLs"""
    print("\nTesting error handling...")
    
    invalid_urls = [
        "https://metruyencv.com/invalid-page-404",
        "https://invalid-domain-that-does-not-exist.com/page"
    ]
    
    try:
        async with MetruyenScraper() as scraper:
            results = await scraper.scrape_urls(invalid_urls)
            
            # Should handle errors gracefully
            print(f"✓ Error handling test completed")
            print(f"  Results: {len(results)} successful out of {len(invalid_urls)}")
            
            # Check error statistics
            stats = scraper.get_statistics()
            error_stats = stats['error_statistics']
            print(f"  Total errors: {error_stats['total_errors']}")
            
            return True
            
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        return False


async def run_all_tests():
    """Run all tests"""
    print("MetruyenScraper Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Scraper Initialization", test_scraper_initialization),
        ("Single URL Scraping", test_single_url_scraping),
        ("Data Export", test_data_export),
        ("Error Handling", test_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)


if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test suite failed: {e}")
        sys.exit(1)
